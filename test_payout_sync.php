<?php

use Magento\Framework\App\Bootstrap;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

// Get the cron job instance
$cronJob = $objectManager->get(\Comave\PayoutManagement\Cron\SyncPayouts::class);

echo "Starting manual payout sync...\n";

try {
    // Execute the cron job
    $cronJob->execute();
    echo "Payout sync completed successfully!\n";
} catch (\Exception $e) {
    echo "Error running payout sync: " . $e->getMessage() . "\n";
}

echo "Check var/log/system.log for detailed logs.\n";
