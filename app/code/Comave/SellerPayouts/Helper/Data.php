<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Helper;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Directory\Model\CurrencyFactory;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Stripe\Account;
use Stripe\Exception\ApiErrorException;
use Stripe\Stripe;

class Data extends ConfigProvider
{
    public function __construct(
        Context $context,
        private readonly CurrencyFactory $currencyFactory,
        private readonly CustomerSession $customerSession,
        private readonly EncryptorInterface $encryptor,
        private readonly Customer $customerModel,
        private readonly StoreManagerInterface $storeManager,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly ManagerInterface $messageManager,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct(
            $context,
            $this->currencyFactory,
            $this->customerSession,
            $this->encryptor,
            $this->customerModel,
            $this->storeManager,
            $this->customerRepository,
            $this->messageManager,
            $this->logger
        );
    }
    /**
     * Get Decrypted Key function
     *
     * @return string
     */
    public function getDecryptedKey(): string
    {
        $key = $this->getStripeKeys();

        return $this->encryptor->decrypt($key['secret_key']);
    }

    /**
     * Check Stripe Account function
     *
     * @param int $customerId
     * @return bool
     */
    public function checkStripeAccount(int $customerId): bool
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            $stripeAccountId = $customer->getCustomAttribute('stripe_client_id');

            return $stripeAccountId && $stripeAccountId->getValue();
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            $this->logger->error('Check Stripe Account function NoSuchEntityException: ' . $e->getMessage());

            return false;
        } catch (\Exception $e) {
            $this->logger->error('Check Stripe Account function Exception: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Create Seller Stripe Account function
     *
     * @param string $email
     * @param string $country
     * @return \Stripe\Account|bool
     */
    public function createSellerStripeAccount()
    {
        $this->logger->info("Hello Creat");
        // $this->logger->info('Starting createSellerStripeAccount', [
        //     'email' => $email,
        //     'country' => $country
        // ]);

        // try {
        //     $stripeKeys = $this->getDecryptedKey();
        // } catch (\Exception $e) {
        //     $this->logger->info('Failed to get Stripe keys', [
        //         'error' => $e->getMessage()
        //     ]);
        //     return false;
        // }

        // // Log the attempt with parameters
        // $this->logger->info('Attempting to create Stripe account', [
        //     'email' => $email,
        //     'country' => $country,
        //     'stripe_key_configured' => !empty($stripeKeys),
        //     'stripe_key_length' => strlen($stripeKeys ?? '')
        // ]);

        // if (empty($stripeKeys)) {
        //     $this->logger->info('Stripe API key is not configured');
        //     return false;
        // }

        // Stripe::setApiKey($stripeKeys);
        // $this->logger->info('Stripe API key set, about to create account');

        // try {
        //     $account = $this->createAccount($email, $country);
        //     $this->logger->info('Stripe account created successfully', [
        //         'account_id' => $account->id,
        //         'country' => $account->country,
        //         'email' => $account->email,
        //         'type' => $account->type
        //     ]);
        //     return $account;
        // } catch (ApiErrorException $e) {
        //     $errorDetails = [
        //         'error_message' => $e->getMessage(),
        //         'error_code' => $e->getStripeCode(),
        //         'error_type' => $e->getError()->type ?? 'unknown',
        //         'error_param' => $e->getError()->param ?? 'unknown',
        //         'email' => $email,
        //         'country' => $country,
        //         'http_status' => $e->getHttpStatus(),
        //         'request_id' => $e->getError()->request_id ?? 'unknown'
        //     ];

        //     $this->logger->info('Create Seller Stripe Account Error', $errorDetails);
        //     return false;
        // } catch (\Exception $e) {
        //     $this->logger->info('General error creating Stripe account', [
        //         'error' => $e->getMessage()
        //     ]);
        //     return false;
        // }
    }

    /**
     * Get Seller Account Status function
     *
     * @param string $accountId
     * @return bool
     */
    public function getSellerAccountStatus(string $accountId): bool
    {
        $stripeKeys = $this->getDecryptedKey();

        try {
            $stripe = new \Stripe\StripeClient($stripeKeys);
            $account = $stripe->accounts->retrieve($accountId, []);

            return $account->payouts_enabled === true;
        } catch (\Stripe\Exception\ApiErrorException $e) {
            $this->logger->error('Stripe API error: ' . $e->getMessage());

            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error retrieving Stripe account: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Create Stripe Login Link function
     *
     * @param string $accountId
     * @return bool|string
     */
    public function createStripeLoginLink(string $accountId): bool|string
    {
        $stripeKeys = $this->getDecryptedKey();
        $stripe = new \Stripe\StripeClient($stripeKeys);

        try {
            // Create the login link for the specified account ID
            $response = $stripe->accounts->createLoginLink($accountId, []);

            // Check if the 'url' key exists in the response
            return $response['url'] ? (string) $response['url'] : false;
        } catch (\Exception $e) {
            $this->logger->error('Error creating Stripe login link: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Delete Seller Stripe Account function
     *
     * @param string $accountId
     * @return bool
     */
    public function deleteSellerStripeAccount(string $accountId): bool
    {
        $stripeKeys = $this->getDecryptedKey();
        Stripe::setApiKey($stripeKeys);

        try {
            $account = Account::retrieve($accountId);
            $account->delete();

            return true;
        } catch (ApiErrorException $e) {
            $this->logger->error($e->getMessage());

            return false;
        }
    }

    /**
     * Stripe account verification link
     *
     * @param string $accountId
     * @return mixed|bool|string|null
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function createStripeAccountLink(string $accountId): mixed
    {
        $stripeKeys = $this->getDecryptedKey();
        Stripe::setApiKey($stripeKeys);
        $baseUrl = $this->storeManager->getStore()->getBaseUrl();

        try {
            $accountLink = \Stripe\AccountLink::create([
                'account' => $accountId,
                'refresh_url' => $baseUrl . 'seller_payouts/seller/payouts',
                'return_url' => $baseUrl . 'seller_payouts/seller/payouts',
                'type' => 'account_onboarding',
            ]);

            return $accountLink['url'];
        } catch (ApiErrorException $e) {
            // Handle exception
            $this->logger->error($e->getMessage());

            return false;
        }
    }

    /**
     * When admin wants to pay to seller from the admin dashboard - manual payouts
     *
     * @param int|float $amount
     * @param int|string $sellerId
     * @param string $wksellerorderids
     * @return bool
     */
    public function manualPayout(
        int|float $amount,
        int|string $sellerId,
        string $wksellerorderids
    ): bool|\Magento\Framework\Phrase {
        $stripeID = $this->getStripeId($sellerId);

        if (empty($stripeID)) {
            $this->logger->error('Please create a Stripe account on the Comave Stripe platform.');

            return false;
        }

        try {
            $this->processPayout($amount, $stripeID, $wksellerorderids);
        } catch (\Exception $e) {
            $this->logger->error('Stripe API Manual Payout Error: ' . $e->getMessage());

            return false;
        }

        return true;
    }

    /**
     * Convert Price function
     *
     * @param float $price
     * @param string $currencyCodeTo
     * @return float
     */
    public function convertPrice(float $price, string $currencyCodeTo): float
    {
        $rate = $this->currencyFactory->create()
            ->load($this->getStoreBaseCurrency())
            ->getAnyRate(strtoupper($currencyCodeTo));

        return $price * $rate;
    }

    /**
     * Get PlatForm Currency function
     *
     * @return bool|string
     */
    public function getPlatFormCurrency(): bool|string
    {
        // Retrieve the decrypted Stripe API key
        $stripeSecretKey = $this->getDecryptedKey();
        $accountId = $this->getPlatformStripeId();

        try {
            // Initialize the Stripe client with the decrypted key
            $stripe = new \Stripe\StripeClient($stripeSecretKey);

            // Retrieve the main account details
            $account = $stripe->accounts->retrieve($accountId);

            // Check if the account is a Stripe Connect account
            if ($account->type === 'standard' || $account->type === 'express') {
                // Retrieve the main account's default_currency
                return $account->default_currency;
            }

            // Log error if the account is not a Stripe Connect account
            $this->logger->error('The provided account is not a Stripe Connect account');

            return false;
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Log Stripe API errors
            $this->logger->error('Stripe API error: ' . $e->getMessage());

            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error retrieving Stripe account: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Initialize Comave Logger function
     *
     * @return \Psr\Log\LoggerInterface
     */
    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * Get Stripe Id function
     *
     * @param int|string $sellerId
     * @return string|null
     */
    private function getStripeId(int|string $sellerId): ?string
    {
        $customer = $this->customerModel->load((int) $sellerId);

        return $customer->getStripeClientId();
    }

    /**
     * Process Payout function
     *
     * @param int|float $amount
     * @param string $stripeID
     * @param string $wksellerorderids
     * @return void
     */
    private function processPayout(int|float $amount, string $stripeID, string $wksellerorderids): void
    {
        $currency = $this->getPlatFormCurrency();
        $stripe = new \Stripe\StripeClient($this->getDecryptedKey());
        $stripe->transfers->create([
            'amount' => (int) round($this->convertPrice($amount, $currency) * 100),
            'currency' => $currency,
            'destination' => $stripeID,
            'transfer_group' => 'ORDER_ID_' . $wksellerorderids,
        ]);
    }

    /**
     * Get Stripe Keys function
     *
     * @param int|string|null $store
     * @return mixed[]
     */
    private function getStripeKeys(int|string|null $store = null): array
    {
        $mode = $this->scopeConfig->getValue(self::XML_ADMIN_STRIPE_MODE, ScopeInterface::SCOPE_STORE, $store);

        return [
            'publishable_key' => $this->scopeConfig->getValue(
                $mode === 'test'
                    ? self::XML_ADMIN_STRIPE_TEST_PUBLISHABLE_KEY
                    : self::XML_ADMIN_STRIPE_LIVE_PUBLISHABLE_KEY,
                ScopeInterface::SCOPE_STORE,
                $store
            ),
            'secret_key' => $this->scopeConfig->getValue(
                $mode === 'test'
                    ? self::XML_ADMIN_STRIPE_TEST_SECRET_KEY
                    : self::XML_ADMIN_STRIPE_LIVE_SECRET_KEY,
                ScopeInterface::SCOPE_STORE,
                $store
            )
        ];
    }

    /**
     * Create a Stripe account with the given parameters.
     *
     * @param string $email
     * @param string $country
     * @return \Stripe\Account
     */
    private function createAccount(string $email, string $country): \Stripe\Account
    {
        $accountData = [
            'capabilities' => [
                'card_payments' => ['requested' => true],
                'transfers' => ['requested' => true],
            ],
            'country' => $country,
            'email' => $email,
            'settings' => [
                'payouts' => [
                    'schedule' => [
                        'interval' => 'monthly',
                        'monthly_anchor' => $this->getSellerPayoutsDays(),
                    ],
                ],
            ],
            'type' => 'express',
        ];

        // Log the exact data being sent to Stripe
        $this->logger->info('Creating Stripe account with data', [
            'account_data' => $accountData,
            'payout_days' => $this->getSellerPayoutsDays()
        ]);

        $this->logger->info('About to call Stripe Account::create', [
            'account_data_json' => json_encode($accountData)
        ]);

        return Account::create($accountData);
    }
}
