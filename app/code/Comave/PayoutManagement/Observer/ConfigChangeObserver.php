<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Observer;

use Comave\PayoutManagement\Service\CronScheduleManager;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;

class ConfigChangeObserver implements ObserverInterface
{
    public function __construct(
        private readonly CronScheduleManager $cronScheduleManager,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Update cron schedule when payout management configuration changes
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        try {
            $changedPaths = $observer->getEvent()->getChangedPaths();
            
            // Check if payout management config was changed
            $payoutConfigChanged = false;
            foreach ($changedPaths as $path) {
                if (strpos($path, 'payout_management/') === 0) {
                    $payoutConfigChanged = true;
                    break;
                }
            }
            
            if ($payoutConfigChanged) {
                $this->logger->info('Payout management configuration changed, updating cron schedule');
                $this->cronScheduleManager->updateCronSchedule();
            }
        } catch (\Exception $e) {
            $this->logger->error('Error updating cron schedule after config change: ' . $e->getMessage());
        }
    }
}
