<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\PayoutManagement\Helper\Config;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class CronScheduleManager
{
    private const JOB_CODE = 'comave_payout_management_sync';
    
    public function __construct(
        private readonly Config $config,
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Check if the cron job should run based on the configured frequency
     *
     * @return bool
     */
    public function shouldRunNow(): bool
    {
        if (!$this->config->isEnabled()) {
            return false;
        }

        $frequency = $this->config->getSyncFrequency();
        $lastRun = $this->getLastSuccessfulRun();
        
        if (!$lastRun) {
            // Never run before, should run now
            return true;
        }

        $nextRunTime = $this->calculateNextRunTime($lastRun, $frequency);
        
        return time() >= $nextRunTime;
    }

    /**
     * Get the timestamp of the last successful run
     *
     * @return int|null
     */
    private function getLastSuccessfulRun(): ?int
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('cron_schedule');
        
        $select = $connection->select()
            ->from($tableName, ['finished_at'])
            ->where('job_code = ?', self::JOB_CODE)
            ->where('status = ?', 'success')
            ->order('finished_at DESC')
            ->limit(1);
            
        $result = $connection->fetchOne($select);
        
        return $result ? strtotime($result) : null;
    }

    /**
     * Calculate next run time based on cron expression
     *
     * @param int $lastRun
     * @param string $cronExpression
     * @return int
     */
    private function calculateNextRunTime(int $lastRun, string $cronExpression): int
    {
        // Parse cron expression and calculate next run time
        // For simplicity, we'll handle the common cases
        switch ($cronExpression) {
            case '*/15 * * * *':
                return $lastRun + (15 * 60); // 15 minutes
            case '0 * * * *':
                return $lastRun + (60 * 60); // 1 hour
            case '0 */6 * * *':
                return $lastRun + (6 * 60 * 60); // 6 hours
            case '0 */12 * * *':
                return $lastRun + (12 * 60 * 60); // 12 hours
            case '0 0 * * *':
                return $lastRun + (24 * 60 * 60); // 24 hours
            case '0 0 * * 0':
                return $lastRun + (7 * 24 * 60 * 60); // 7 days
            default:
                // Default to 6 hours if unknown
                return $lastRun + (6 * 60 * 60);
        }
    }

    /**
     * Update cron schedule table with new frequency
     *
     * @return void
     */
    public function updateCronSchedule(): void
    {
        if (!$this->config->isEnabled()) {
            $this->logger->info('Payout management is disabled, skipping cron schedule update');
            return;
        }

        $frequency = $this->config->getSyncFrequency();
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('cron_schedule');
        
        // Remove pending schedules for this job
        $connection->delete($tableName, [
            'job_code = ?' => self::JOB_CODE,
            'status = ?' => 'pending'
        ]);
        
        // Add new schedule based on frequency
        $nextRun = $this->calculateNextRunFromNow($frequency);
        
        $connection->insert($tableName, [
            'job_code' => self::JOB_CODE,
            'status' => 'pending',
            'scheduled_at' => date('Y-m-d H:i:s', $nextRun)
        ]);
        
        $this->logger->info('Updated cron schedule for payout sync', [
            'frequency' => $frequency,
            'next_run' => date('Y-m-d H:i:s', $nextRun)
        ]);
    }

    /**
     * Calculate next run time from now
     *
     * @param string $cronExpression
     * @return int
     */
    private function calculateNextRunFromNow(string $cronExpression): int
    {
        $now = time();
        
        switch ($cronExpression) {
            case '*/15 * * * *':
                // Next 15-minute mark
                return $now + (15 * 60) - ($now % (15 * 60));
            case '0 * * * *':
                // Next hour
                return $now + (60 * 60) - ($now % (60 * 60));
            case '0 */6 * * *':
                // Next 6-hour mark
                return $now + (6 * 60 * 60) - ($now % (6 * 60 * 60));
            case '0 */12 * * *':
                // Next 12-hour mark
                return $now + (12 * 60 * 60) - ($now % (12 * 60 * 60));
            case '0 0 * * *':
                // Next midnight
                return strtotime('tomorrow midnight');
            case '0 0 * * 0':
                // Next Sunday midnight
                return strtotime('next sunday midnight');
            default:
                // Default to next hour
                return $now + (60 * 60) - ($now % (60 * 60));
        }
    }
}
