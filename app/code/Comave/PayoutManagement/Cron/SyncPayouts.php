<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Cron;

use Comave\PayoutManagement\Service\PayoutSyncService;
use Comave\PayoutManagement\Helper\Config;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class SyncPayouts
{
    private const JOB_CODE = 'comave_payout_management_sync';

    public function __construct(
        private readonly PayoutSyncService $payoutSyncService,
        private readonly Config $config,
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Execute cron job to sync payouts
     *
     * @return void
     */
    public function execute(): void
    {
        try {
            // Check if module is enabled
            if (!$this->config->isEnabled()) {
                $this->logger->info('Payout management is disabled, skipping sync');
                return;
            }

            $this->logger->info('Starting scheduled payout synchronization');

            $result = $this->payoutSyncService->syncPayouts();

            if ($result['success']) {
                $this->logger->info(
                    'Payout synchronization completed successfully',
                    ['updated' => $result['updated'], 'added' => $result['added']]
                );

                // Schedule next run
                $this->scheduleNextRun();
            } else {
                $this->logger->error('Payout synchronization failed: ' . $result['error']);

                // Still schedule next run even if this one failed
                $this->scheduleNextRun();
            }
        } catch (\Exception $e) {
            $this->logger->error('Error in scheduled payout sync: ' . $e->getMessage());

            // Schedule next run even if there was an error
            $this->scheduleNextRun();
        }
    }

    /**
     * Schedule the next run based on configured frequency
     *
     * @return void
     */
    private function scheduleNextRun(): void
    {
        try {
            $frequency = $this->config->getSyncFrequency();
            $nextRun = $this->calculateNextRun($frequency);

            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            // Check if there's already a pending schedule
            $existingSchedule = $connection->fetchOne(
                $connection->select()
                    ->from($tableName, 'schedule_id')
                    ->where('job_code = ?', self::JOB_CODE)
                    ->where('status = ?', 'pending')
                    ->where('scheduled_at > ?', date('Y-m-d H:i:s'))
            );

            if (!$existingSchedule) {
                // Insert new schedule
                $connection->insert($tableName, [
                    'job_code' => self::JOB_CODE,
                    'status' => 'pending',
                    'scheduled_at' => date('Y-m-d H:i:s', $nextRun),
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                $this->logger->info('Scheduled next payout sync', [
                    'frequency' => $frequency,
                    'next_run' => date('Y-m-d H:i:s', $nextRun)
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error scheduling next payout sync: ' . $e->getMessage());
        }
    }

    /**
     * Calculate next run time based on cron expression
     *
     * @param string $cronExpression
     * @return int
     */
    private function calculateNextRun(string $cronExpression): int
    {
        $now = time();

        switch ($cronExpression) {
            case '*/15 * * * *':
                return $now + (15 * 60);
            case '0 * * * *':
                return $now + (60 * 60);
            case '0 */6 * * *':
                return $now + (6 * 60 * 60);
            case '0 */12 * * *':
                return $now + (12 * 60 * 60);
            case '0 0 * * *':
                return $now + (24 * 60 * 60);
            case '0 0 * * 0':
                return $now + (7 * 24 * 60 * 60);
            default:
                // Default to 6 hours
                return $now + (6 * 60 * 60);
        }
    }
}
