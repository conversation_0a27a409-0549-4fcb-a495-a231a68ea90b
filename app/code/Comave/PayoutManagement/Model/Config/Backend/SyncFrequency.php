<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\Config\Backend;

use Magento\Framework\App\Config\Value;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;

class SyncFrequency extends Value
{
    private const JOB_CODE = 'comave_payout_management_sync';
    
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        ScopeConfigInterface $config,
        \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList,
        private readonly ResourceConnection $resourceConnection,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $config, $cacheTypeList, $resource, $resourceCollection, $data);
    }

    /**
     * Update cron schedule when frequency changes
     *
     * @return $this
     */
    public function afterSave()
    {
        $frequency = $this->getValue();
        
        if ($frequency) {
            $this->updateCronSchedule($frequency);
        }
        
        return parent::afterSave();
    }

    /**
     * Update cron schedule in database
     *
     * @param string $frequency
     * @return void
     */
    private function updateCronSchedule(string $frequency): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');
            
            // Remove pending schedules for this job
            $connection->delete($tableName, [
                'job_code = ?' => self::JOB_CODE,
                'status = ?' => 'pending'
            ]);
            
            // Calculate next run time based on frequency
            $nextRun = $this->calculateNextRun($frequency);
            
            // Insert new schedule
            $connection->insert($tableName, [
                'job_code' => self::JOB_CODE,
                'status' => 'pending',
                'scheduled_at' => date('Y-m-d H:i:s', $nextRun),
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $this->_logger->info('Updated cron schedule for payout sync', [
                'frequency' => $frequency,
                'next_run' => date('Y-m-d H:i:s', $nextRun)
            ]);
            
        } catch (\Exception $e) {
            $this->_logger->error('Error updating cron schedule: ' . $e->getMessage());
        }
    }

    /**
     * Calculate next run time based on cron expression
     *
     * @param string $cronExpression
     * @return int
     */
    private function calculateNextRun(string $cronExpression): int
    {
        $now = time();
        
        switch ($cronExpression) {
            case '*/15 * * * *':
                // Next 15-minute mark
                return $now + (15 * 60) - ($now % (15 * 60));
            case '0 * * * *':
                // Next hour
                return $now + (60 * 60) - ($now % (60 * 60));
            case '0 */6 * * *':
                // Next 6-hour mark
                return $now + (6 * 60 * 60) - ($now % (6 * 60 * 60));
            case '0 */12 * * *':
                // Next 12-hour mark
                return $now + (12 * 60 * 60) - ($now % (12 * 60 * 60));
            case '0 0 * * *':
                // Next midnight
                return strtotime('tomorrow midnight');
            case '0 0 * * 0':
                // Next Sunday midnight
                return strtotime('next sunday midnight');
            default:
                // Default to next hour
                return $now + (60 * 60) - ($now % (60 * 60));
        }
    }
}
