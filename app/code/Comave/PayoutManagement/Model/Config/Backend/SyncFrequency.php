<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\Config\Backend;

use Magento\Framework\App\Config\Value;
use Magento\Framework\App\Config\ValueFactory;
use Magento\Framework\App\ResourceConnection;
use Magento\Cron\Model\Schedule;

class SyncFrequency extends Value
{
    private const CRON_STRING_PATH = 'crontab/default/jobs/comave_payout_management_sync/schedule/cron_expr';
    private const CRON_MODEL_PATH = 'crontab/default/jobs/comave_payout_management_sync/run/model';

    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\App\Config\ScopeConfigInterface $config,
        \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList,
        private readonly ValueFactory $configValueFactory,
        private readonly ResourceConnection $resourceConnection,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $config, $cacheTypeList, $resource, $resourceCollection, $data);
    }

    /**
     * Update cron expression when frequency changes
     *
     * @return $this
     * @throws \Exception
     */
    public function afterSave()
    {
        $frequency = $this->getValue();

        if ($frequency) {
            try {
                // Clear old pending schedules first
                $this->clearOldSchedules();

                // Update the cron expression in config
                $this->configValueFactory->create()->load(
                    self::CRON_STRING_PATH,
                    'path'
                )->setValue(
                    $frequency
                )->setPath(
                    self::CRON_STRING_PATH
                )->save();

                // Update the model path
                $this->configValueFactory->create()->load(
                    self::CRON_MODEL_PATH,
                    'path'
                )->setValue(
                    'Comave\PayoutManagement\Cron\SyncPayouts::execute'
                )->setPath(
                    self::CRON_MODEL_PATH
                )->save();

                $this->_logger->info('Updated cron expression for payout sync', [
                    'frequency' => $frequency,
                    'cron_path' => self::CRON_STRING_PATH
                ]);

                // Create new schedules with the new frequency
                $this->createNewSchedules($frequency);

            } catch (\Exception $e) {
                $this->_logger->error('Error updating cron expression: ' . $e->getMessage());
                throw new \Magento\Framework\Exception\CouldNotSaveException(__('We can\'t save the cron expression.'));
            }
        }

        return parent::afterSave();
    }

    /**
     * Clear old pending schedules when frequency changes
     *
     * @return void
     */
    private function clearOldSchedules(): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            // Delete all pending schedules for this job
            $deletedCount = $connection->delete($tableName, [
                'job_code = ?' => 'comave_payout_management_sync',
                'status = ?' => 'pending'
            ]);

            $this->_logger->info('Cleared old cron schedules', [
                'job_code' => 'comave_payout_management_sync',
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            $this->_logger->error('Error clearing old schedules: ' . $e->getMessage());
        }
    }

    /**
     * Create new schedules with the updated frequency
     *
     * @param string $frequency
     * @return void
     */
    private function createNewSchedules(string $frequency): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            // Generate next few schedules based on frequency
            $schedules = $this->generateScheduleTimes($frequency, 5); // Generate next 5 schedules

            foreach ($schedules as $scheduleTime) {
                $connection->insert($tableName, [
                    'job_code' => 'comave_payout_management_sync',
                    'status' => 'pending',
                    'scheduled_at' => date('Y-m-d H:i:s', $scheduleTime),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $this->_logger->info('Created new cron schedules', [
                'job_code' => 'comave_payout_management_sync',
                'frequency' => $frequency,
                'schedules_created' => count($schedules),
                'next_run' => date('Y-m-d H:i:s', $schedules[0] ?? time())
            ]);

        } catch (\Exception $e) {
            $this->_logger->error('Error creating new schedules: ' . $e->getMessage());
        }
    }

    /**
     * Generate schedule times based on cron expression
     *
     * @param string $cronExpression
     * @param int $count
     * @return array
     */
    private function generateScheduleTimes(string $cronExpression, int $count = 5): array
    {
        $schedules = [];
        $now = time();

        switch ($cronExpression) {
            case '* * * * *':
                // Every minute
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 60);
                }
                break;
            case '*/15 * * * *':
                // Every 15 minutes
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 15 * 60);
                }
                break;
            case '0 * * * *':
                // Every hour
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 60 * 60);
                }
                break;
            case '0 */6 * * *':
                // Every 6 hours
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 6 * 60 * 60);
                }
                break;
            case '0 */12 * * *':
                // Every 12 hours
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 12 * 60 * 60);
                }
                break;
            case '0 0 * * *':
                // Daily
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 24 * 60 * 60);
                }
                break;
            case '0 0 * * 0':
                // Weekly
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 7 * 24 * 60 * 60);
                }
                break;
            default:
                // Default to hourly
                for ($i = 1; $i <= $count; $i++) {
                    $schedules[] = $now + ($i * 60 * 60);
                }
        }

        return $schedules;
    }
}
