<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\Config\Backend;

use Magento\Framework\App\Config\Value;
use Magento\Framework\App\Config\ValueFactory;
use Magento\Framework\App\ResourceConnection;

class SyncFrequency extends Value
{
    private const CRON_STRING_PATH = 'crontab/default/jobs/comave_payout_management_sync/schedule/cron_expr';
    private const CRON_MODEL_PATH = 'crontab/default/jobs/comave_payout_management_sync/run/model';

    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\App\Config\ScopeConfigInterface $config,
        \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList,
        private readonly ValueFactory $configValueFactory,
        private readonly ResourceConnection $resourceConnection,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $config, $cacheTypeList, $resource, $resourceCollection, $data);
    }

    /**
     * Update cron expression when frequency changes
     *
     * @return $this
     * @throws \Exception
     */
    public function afterSave()
    {
        $frequency = $this->getValue();

        if ($frequency) {
            try {
                // Update the cron expression in config
                $this->configValueFactory->create()->load(
                    self::CRON_STRING_PATH,
                    'path'
                )->setValue(
                    $frequency
                )->setPath(
                    self::CRON_STRING_PATH
                )->save();

                // Update the model path
                $this->configValueFactory->create()->load(
                    self::CRON_MODEL_PATH,
                    'path'
                )->setValue(
                    'Comave\PayoutManagement\Cron\SyncPayouts::execute'
                )->setPath(
                    self::CRON_MODEL_PATH
                )->save();

                $this->_logger->info('Updated cron expression for payout sync', [
                    'frequency' => $frequency,
                    'cron_path' => self::CRON_STRING_PATH
                ]);

                // Create initial schedule
                $this->createInitialSchedule($frequency);

            } catch (\Exception $e) {
                $this->_logger->error('Error updating cron expression: ' . $e->getMessage());
                throw new \Magento\Framework\Exception\CouldNotSaveException(__('We can\'t save the cron expression.'));
            }
        }

        return parent::afterSave();
    }

    /**
     * Create initial schedule after config change
     *
     * @param string $frequency
     * @return void
     */
    private function createInitialSchedule(string $frequency): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            // Remove any existing pending schedules for this job
            $connection->delete($tableName, [
                'job_code = ?' => 'comave_payout_management_sync',
                'status = ?' => 'pending'
            ]);

            // Calculate next run time
            $nextRun = $this->calculateNextRun($frequency);

            // Insert new schedule
            $connection->insert($tableName, [
                'job_code' => 'comave_payout_management_sync',
                'status' => 'pending',
                'scheduled_at' => date('Y-m-d H:i:s', $nextRun),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $this->_logger->info('Created initial cron schedule', [
                'frequency' => $frequency,
                'next_run' => date('Y-m-d H:i:s', $nextRun)
            ]);

        } catch (\Exception $e) {
            $this->_logger->error('Error creating initial schedule: ' . $e->getMessage());
        }
    }

    /**
     * Calculate next run time based on cron expression
     *
     * @param string $cronExpression
     * @return int
     */
    private function calculateNextRun(string $cronExpression): int
    {
        $now = time();

        switch ($cronExpression) {
            case '* * * * *':
                return $now + 60; // Next minute
            case '*/15 * * * *':
                return $now + (15 * 60); // 15 minutes from now
            case '0 * * * *':
                return $now + (60 * 60); // Next hour
            case '0 */6 * * *':
                return $now + (6 * 60 * 60); // 6 hours from now
            case '0 */12 * * *':
                return $now + (12 * 60 * 60); // 12 hours from now
            case '0 0 * * *':
                return strtotime('tomorrow midnight'); // Next midnight
            case '0 0 * * 0':
                return strtotime('next sunday midnight'); // Next Sunday
            default:
                return $now + (60 * 60); // Default to 1 hour
        }
    }
}
