<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="payout_management" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Payout Management</label>
            <tab>sales</tab>
            <resource>Comave_PayoutManagement::payout_management</resource>
            
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>

                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Payout Management</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable/disable the payout management functionality</comment>
                </field>

                <field id="sync_frequency" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sync Frequency</label>
                    <source_model>Comave\PayoutManagement\Model\Config\Source\SyncFrequency</source_model>
                    <backend_model>Comave\PayoutManagement\Model\Config\Backend\SyncFrequency</backend_model>
                    <comment>How often to sync payout data from Stripe</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="enable_notifications" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Email Notifications</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Send email notifications for payout status changes</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
